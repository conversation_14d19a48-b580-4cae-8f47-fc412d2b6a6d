#!/usr/bin/env python3
"""
测试打断逻辑优化的脚本
验证 realtime_chat_v1 的打断逻辑改进是否正确应用到 app.py
"""

import sys
import asyncio
from pathlib import Path

# 添加路径
sys.path.append(str(Path(__file__).parent / "thirdparty" / "realtime_chat_v1"))
sys.path.append(str(Path(__file__).parent / "thirdparty" / "realtime_chat"))
sys.path.append(str(Path(__file__).parent / "thirdparty" / "tb_speech_search"))

def test_imports():
    """测试导入是否正常"""
    print("🔍 测试模块导入...")
    
    try:
        from tts8 import ByteDanceTTSStreaming, StreamConfig, TriggerStrategy
        print("✅ tts8 模块导入成功")
    except Exception as e:
        print(f"❌ tts8 模块导入失败: {e}")
        return False
    
    try:
        from chat import InterruptibleAzureOpenAI
        print("✅ chat 模块导入成功")
    except Exception as e:
        print(f"❌ chat 模块导入失败: {e}")
        return False
    
    try:
        from system_prompt import system_prompt
        print("✅ system_prompt 模块导入成功")
        print(f"📝 system_prompt 长度: {len(system_prompt)} 字符")
    except Exception as e:
        print(f"❌ system_prompt 模块导入失败: {e}")
        return False
    
    try:
        from custom_tools import TaobaoMainSearchTool, TaobaoItemDetailsTool
        print("✅ custom_tools 模块导入成功")
    except Exception as e:
        print(f"❌ custom_tools 模块导入失败: {e}")
        return False
    
    try:
        from search_agent import fetch_rag
        print("✅ search_agent 模块导入成功")
    except Exception as e:
        print(f"❌ search_agent 模块导入失败: {e}")
        return False
    
    return True

async def test_clear_queue_function():
    """测试异步清空队列函数"""
    print("\n🔍 测试异步清空队列函数...")
    
    # 创建测试队列
    test_queue = asyncio.Queue()
    
    # 添加一些测试项目
    for i in range(5):
        await test_queue.put(f"test_item_{i}")
    
    print(f"📊 队列初始大小: {test_queue.qsize()}")
    
    # 定义清空队列函数（从 app.py 复制）
    async def clear_queue(queue):
        """异步清空队列的全局函数"""
        while not queue.empty():
            try:
                item = await queue.get()
                queue.task_done()
            except asyncio.QueueEmpty:
                break
    
    # 清空队列
    await clear_queue(test_queue)
    
    print(f"📊 队列清空后大小: {test_queue.qsize()}")
    
    if test_queue.qsize() == 0:
        print("✅ 异步清空队列函数工作正常")
        return True
    else:
        print("❌ 异步清空队列函数未能完全清空队列")
        return False

def test_interrupt_logic_structure():
    """测试打断逻辑结构"""
    print("\n🔍 测试打断逻辑结构...")
    
    # 检查 app.py 中的关键方法是否存在
    try:
        with open("app.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查关键方法
        required_methods = [
            "async def interrupt(self, conv_id):",
            "async def clear_queue_async(self):",
            "def clear_queue(self):",
            "async def clear_queue(queue):",
        ]
        
        missing_methods = []
        for method in required_methods:
            if method not in content:
                missing_methods.append(method)
        
        if missing_methods:
            print(f"❌ 缺少以下方法: {missing_methods}")
            return False
        else:
            print("✅ 所有必需的打断逻辑方法都存在")
        
        # 检查打断逻辑的调用顺序
        if "await self.voice_queue.put(AdditionalOutputs(interrupt_event))" in content:
            print("✅ 打断事件发送逻辑存在")
        else:
            print("❌ 打断事件发送逻辑缺失")
            return False
        
        if "self.clear_queue()" in content:
            print("✅ 同步清空队列调用存在")
        else:
            print("❌ 同步清空队列调用缺失")
            return False
        
        if "await self.interrupt(conv_id=conv_id)" in content:
            print("✅ 打断方法调用存在")
        else:
            print("❌ 打断方法调用缺失")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 读取 app.py 文件失败: {e}")
        return False

def test_v1_improvements():
    """测试 v1 版本的改进是否应用"""
    print("\n🔍 测试 v1 版本改进应用情况...")
    
    try:
        with open("app.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        improvements = []
        
        # 检查是否使用了 v1 版本的路径
        if "realtime_chat_v1" in content:
            improvements.append("✅ 使用了 realtime_chat_v1 路径")
        else:
            improvements.append("❌ 未使用 realtime_chat_v1 路径")
        
        # 检查是否有优化的打断逻辑注释
        if "参考realtime_chat_v1" in content:
            improvements.append("✅ 包含 v1 版本参考注释")
        else:
            improvements.append("❌ 缺少 v1 版本参考注释")
        
        # 检查是否有改进的错误处理
        if "print(f\"🧠 LLM打断完成:" in content:
            improvements.append("✅ 包含改进的调试输出")
        else:
            improvements.append("❌ 缺少改进的调试输出")
        
        # 检查是否有并行打断逻辑
        if "tasks = [interrupt_llm(conv_id), interrupt_tts(conv_id)]" in content:
            improvements.append("✅ 包含并行打断逻辑")
        else:
            improvements.append("❌ 缺少并行打断逻辑")
        
        for improvement in improvements:
            print(improvement)
        
        success_count = sum(1 for imp in improvements if imp.startswith("✅"))
        total_count = len(improvements)
        
        print(f"\n📊 改进应用率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
        
        return success_count == total_count
        
    except Exception as e:
        print(f"❌ 检查 v1 改进失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始测试打断逻辑优化...")
    print("=" * 50)
    
    tests = [
        ("模块导入测试", test_imports),
        ("异步清空队列测试", test_clear_queue_function),
        ("打断逻辑结构测试", test_interrupt_logic_structure),
        ("v1 版本改进测试", test_v1_improvements),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 执行: {test_name}")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 执行失败: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    success_count = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            success_count += 1
    
    total_tests = len(results)
    print(f"\n🎯 总体结果: {success_count}/{total_tests} 测试通过 ({success_count/total_tests*100:.1f}%)")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！打断逻辑优化成功应用。")
    else:
        print("⚠️  部分测试失败，需要进一步检查。")
    
    return success_count == total_tests

if __name__ == "__main__":
    asyncio.run(main())
