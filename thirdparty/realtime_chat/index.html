<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Real-Time Voice Chat</title>
    <style>
        body {
            font-family: "SF Pro Display", -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            color: #ffffff;
            margin: 0;
            padding: 20px;
            height: 100vh;
            box-sizing: border-box;
            overflow: hidden;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            height: calc(100% - 100px);
            display: flex;
            gap: 20px;
        }
        .logo {
            text-align: center;
            margin-bottom: 16px;
            grid-column: 1 / -1;
        }
        .logo h1 {
            font-size: 22px;
            font-weight: 500;
            letter-spacing: 0.3px;
            margin: 0;
            color: #ffffff;
        }
        .phone-icon {
            font-size: 36px;
            margin-bottom: 6px;
            filter: grayscale(0.2);
        }
        .main-content {
            display: flex;
            gap: 20px;
            width: 100%;
            height: 100%;
        }
        .chat-section {
            flex: 1;
            min-width: 400px;
            display: flex;
            flex-direction: column;
        }
        .chat-container {
            border: none;
            border-radius: 20px;
            padding: 0;
            height: 90%;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            background: #ffffff;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
        }
        .product-section {
            flex: 1;
            min-width: 400px;
            display: flex;
            flex-direction: column;
        }
        .product-container {
            border: 1px solid #333;
            padding: 20px;
            height: 90%;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
            background-color: #111;
        }
        .product-header {
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #333;
        }
        .product-header h3 {
            margin: 0;
            color: #1890ff;
            font-size: 18px;
        }
        .product-carousel {
            flex: 1;
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }
        .product-display {
            width: 100%;
            max-width: 350px;
            position: relative;
            background: #1a1a1a;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            transition: transform 0.3s ease;
        }
        .product-display:hover {
            transform: translateY(-2px);
        }
        .product-image-container {
            width: 100%;
            height: 200px;
            margin-bottom: 16px;
            position: relative;
            border-radius: 8px;
            overflow: hidden;
            background: #333;
        }
        .product-image {
            width: 100%;
            height: 100%;
            object-fit: contain;
            background-color: #f9f9f9;
        }
        .product-details {
            text-align: center;
        }
        .product-title {
            color: #fff;
            font-size: 16px;
            line-height: 1.4;
            margin-bottom: 12px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .product-price {
            color: #ff6700;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        .product-price::before {
            content: "¥";
            font-size: 18px;
        }
        .product-meta {
            color: #999;
            font-size: 12px;
            line-height: 1.3;
            margin-bottom: 16px;
        }
        .nav-button {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            z-index: 10;
            color: #fff;
            font-size: 18px;
            transition: all 0.2s ease;
        }
        .nav-button:hover {
            background-color: #ff6700;
            border-color: #ff6700;
            transform: translateY(-50%) scale(1.1);
        }
        .nav-button:disabled {
            opacity: 0.3;
            cursor: not-allowed;
        }
        .nav-button:disabled:hover {
            background-color: rgba(255, 255, 255, 0.1);
            border-color: rgba(255, 255, 255, 0.2);
            transform: translateY(-50%) scale(1);
        }
        .prev-button {
            left: -60px;
        }
        .next-button {
            right: -60px;
        }
        .product-indicator {
            display: flex;
            justify-content: center;
            margin-top: 20px;
            gap: 8px;
        }
        .indicator-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #444;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .indicator-dot.active {
            background-color: #ff6700;
            width: 16px;
            border-radius: 4px;
        }
        .indicator-dot:hover {
            background-color: #666;
        }
        .indicator-dot.active:hover {
            background-color: #ff8533;
        }
        .select-button {
            background-color: #ff6700;
            color: white;
            border: none;
            border-radius: 20px;
            padding: 10px 24px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            margin-top: 12px;
        }
        .select-button:hover {
            background-color: #ff4e00;
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(255, 103, 0, 0.3);
        }
        .no-products {
            text-align: center;
            color: #666;
            padding: 40px 20px;
            font-style: italic;
        }
        .voice-control-hint {
            margin-top: 10px;
            color: #666;
            font-size: 11px;
            text-align: center;
            opacity: 0.7;
            padding: 5px 10px;
        }
        .chat-messages {
            flex-grow: 1;
            overflow-y: auto;
            overflow-x: hidden;
            padding: 20px 16px;
            background: #f8f9fa;
            border-radius: 20px 20px 0 0;
            scroll-behavior: smooth;
        }
        .chat-messages:empty::before {
            content: "Start a call to begin the conversation";
            display: block;
            text-align: center;
            color: #9ca3af;
            font-size: 14px;
            margin-top: 40px;
        }
        .message-wrapper {
            display: flex;
            margin-bottom: 20px;
            align-items: flex-end;
            animation: slideIn 0.3s ease-out;
        }
        .message-wrapper.user {
            justify-content: flex-end;
        }
        .message-wrapper.assistant {
            justify-content: flex-start;
        }
        .message {
            max-width: 75%;
            padding: 12px 18px;
            font-size: 16px;
            line-height: 1.5;
            word-wrap: break-word;
            position: relative;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            font-weight: 400;
            letter-spacing: 0.01em;
        }
        .message.user {
            background-color: #4169ff;
            color: white;
            border-radius: 24px 24px 4px 24px;
            margin-left: auto;
            box-shadow: 0 2px 8px rgba(65, 105, 255, 0.3);
        }
        .message.assistant {
            background-color: #ffffff;
            color: #2c3e50;
            border-radius: 24px 24px 24px 4px;
            box-shadow: 0 1px 6px rgba(0, 0, 0, 0.06);
            border: 1px solid rgba(0, 0, 0, 0.04);
        }
        /* Message animation */
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        /* Chat input area placeholder */
        .chat-input-area {
            padding: 16px;
            background: white;
            border-top: 1px solid #e9ecef;
            border-radius: 0 0 20px 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6c757d;
            font-size: 14px;
        }
        .controls {
            text-align: center;
            margin-top: 10px;
            padding: 10px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        .phone-button {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            color: white;
            position: relative;
            overflow: visible;
        }
        .phone-button.call {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            box-shadow: 0 4px 20px rgba(76, 175, 80, 0.4);
        }
        .phone-button.call:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 30px rgba(76, 175, 80, 0.6);
        }
        .phone-button.call:active {
            transform: scale(0.95);
        }
        .phone-button.hangup {
            background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
            box-shadow: 0 4px 20px rgba(244, 67, 54, 0.4);
        }
        .phone-button.hangup:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 30px rgba(244, 67, 54, 0.6);
        }
        .phone-button.hangup:active {
            transform: scale(0.95);
        }
        .phone-button.connecting {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
            animation: pulse 1.5s infinite;
            cursor: not-allowed;
        }
        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(255, 152, 0, 0.7);
            }
            70% {
                box-shadow: 0 0 0 20px rgba(255, 152, 0, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(255, 152, 0, 0);
            }
        }
        .call-status {
            margin-top: 10px;
            font-size: 14px;
            text-align: center;
            opacity: 0.8;
            min-height: 10px;
        }
        .call-timer {
            font-size: 18px;
            font-weight: 300;
            letter-spacing: 1px;
            color: #4CAF50;
        }
        #audio-output {
            display: none;
        }
        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.6);
            transform: scale(0);
            animation: ripple 0.6s ease-out;
        }
        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }
        /* Audio level indicator */
        .audio-indicator {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100%;
            height: 100%;
            border-radius: 50%;
            border: 3px solid rgba(255, 255, 255, 0.3);
            pointer-events: none;
            transition: transform 0.1s ease;
        }
        .audio-indicator.active {
            transform: translate(-50%, -50%) scale(var(--audio-level, 1));
        }
        /* Microphone audio visualization */
        .mic-container {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            pointer-events: none;
            z-index: 2;
        }
        .mic-icon {
            font-size: 24px;
            color: white;
            position: relative;
            z-index: 3;
        }
        .audio-ring {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            border-radius: 50%;
            border: 2px solid rgba(255, 255, 255, 0.6);
            opacity: 0;
            pointer-events: none;
        }
        .audio-ring-1 {
            width: 60px;
            height: 60px;
            animation: audioRing1 2s ease-out infinite;
        }
        .audio-ring-2 {
            width: 80px;
            height: 80px;
            animation: audioRing2 2s ease-out infinite;
            animation-delay: 0.3s;
        }
        .audio-ring-3 {
            width: 100px;
            height: 100px;
            animation: audioRing3 2s ease-out infinite;
            animation-delay: 0.6s;
        }
        @keyframes audioRing1 {
            0% {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.8);
            }
            20% {
                opacity: calc(var(--audio-level, 0) * 0.8);
            }
            100% {
                opacity: 0;
                transform: translate(-50%, -50%) scale(1.2);
            }
        }
        @keyframes audioRing2 {
            0% {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.8);
            }
            20% {
                opacity: calc(var(--audio-level, 0) * 0.6);
            }
            100% {
                opacity: 0;
                transform: translate(-50%, -50%) scale(1.4);
            }
        }
        @keyframes audioRing3 {
            0% {
                opacity: 0;
                transform: translate(-50%, -50%) scale(0.8);
            }
            20% {
                opacity: calc(var(--audio-level, 0) * 0.4);
            }
            100% {
                opacity: 0;
                transform: translate(-50%, -50%) scale(1.6);
            }
        }
        /* Disable animations if user prefers reduced motion */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        .phone-button.hangup .audio-ring {
            animation-duration: 1.5s;
        }
        .mic-container {
            display: none;
        }
        .phone-button.hangup .mic-container {
            display: block;
        }
        .phone-button.hangup #phone-icon {
            display: none;
        }
        .hangup-icon {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 24px;
            display: none;
            z-index: 4;
        }
        .phone-button.hangup .hangup-icon {
            display: block;
        }
        /* Voice activity indicator */
        .voice-indicator {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 8px 20px;
            background-color: rgba(255, 152, 0, 0.9);
            color: white;
            border-radius: 20px;
            font-size: 12px;
            display: none;
            align-items: center;
            gap: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }
        .voice-indicator.active {
            display: flex;
        }
        .voice-indicator::before {
            content: "🎤";
            font-size: 16px;
            animation: pulse 1s infinite;
        }
        /* Scrollbar styling */
        .chat-messages::-webkit-scrollbar {
            width: 6px;
        }
        .chat-messages::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.05);
            border-radius: 3px;
        }
        .chat-messages::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.15);
            border-radius: 3px;
        }
        .chat-messages::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 0, 0, 0.25);
        }

        /* Fix for edge flickering */
        * {
            -webkit-backface-visibility: hidden;
            -moz-backface-visibility: hidden;
            backface-visibility: hidden;
        }

        /* Prevent layout shifts */
        .chat-container {
            transform: translateZ(0);
            will-change: transform;
        }
        /* Toast notifications */
        .toast {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            padding: 16px 24px;
            border-radius: 8px;
            font-size: 14px;
            z-index: 1000;
            display: none;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
        }
        .toast.error {
            background-color: rgba(244, 67, 54, 0.9);
            color: white;
        }
        .toast.warning {
            background-color: rgba(255, 152, 0, 0.9);
            color: white;
        }
        .toast.info {
            background-color: rgba(33, 150, 243, 0.9);
            color: white;
        }
        /* Audio status */
        .audio-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            z-index: 1000;
            display: none;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }
        .audio-status.good {
            background-color: rgba(76, 175, 80, 0.9);
            color: white;
        }
        .audio-status.poor {
            background-color: rgba(255, 152, 0, 0.9);
            color: white;
        }
        .audio-status.bad {
            background-color: rgba(244, 67, 54, 0.9);
            color: white;
        }
    </style>
</head>

<body>
    <!-- Toast notifications -->
    <div id="error-toast" class="toast"></div>
    <div id="audio-status" class="audio-status"></div>
    <div id="voice-indicator" class="voice-indicator">Speaking detected - Audio paused</div>

    <div class="container">
        <div class="logo">
            <h1>实时语音商品搜索</h1>
        </div>
        <div class="main-content">
            <div class="chat-section">
                <div class="chat-container">
                    <div class="chat-messages" id="chat-messages"></div>
                </div>
                <div class="controls">
                    <button id="phone-button" class="phone-button call">
                        <span id="phone-icon">📞</span>
                        <span class="hangup-icon">❌</span>
                        <div class="audio-indicator" id="audio-indicator"></div>
                        <div class="mic-container" id="mic-container">
                            <div class="audio-ring audio-ring-1"></div>
                            <div class="audio-ring audio-ring-2"></div>
                            <div class="audio-ring audio-ring-3"></div>
                            <span class="mic-icon">🎤</span>
                        </div>
                    </button>
                    <div class="call-status" id="call-status"></div>
                </div>
            </div>
            <div class="product-section">
                <div class="product-container">
                    <div class="product-header">
                        <h3>🛍️ 商品搜索结果</h3>
                    </div>
                    <div class="product-carousel" id="product-carousel">
                        <div class="no-products" id="no-products">
                            暂无搜索结果<br>
                            请开始语音对话并说出您想要搜索的商品
                        </div>
                        <div class="product-display" id="product-display" style="display: none;">
                            <button class="nav-button prev-button" id="prev-button" onclick="showPreviousProduct()">❮</button>
                            <button class="nav-button next-button" id="next-button" onclick="showNextProduct()">❯</button>
                            <div class="product-image-container">
                                <img class="product-image" id="product-image" src="" alt="">
                            </div>
                            <div class="product-details">
                                <div class="product-title" id="product-title"></div>
                                <div class="product-price" id="product-price"></div>
                                <div class="product-meta" id="product-meta"></div>
                                <button class="select-button" id="select-button" onclick="selectCurrentProduct()">选择此商品</button>
                            </div>
                            <div class="voice-control-hint">
                                💬 语音控制：说"左滑"、"右滑"、"选择商品"
                            </div>
                        </div>
                        <div class="product-indicator" id="product-indicator"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <audio id="audio-output"></audio>

    <script>
        let peerConnection;
        let webrtc_id;
        const audioOutput = document.getElementById('audio-output');
        const phoneButton = document.getElementById('phone-button');
        const phoneIcon = document.getElementById('phone-icon');
        const callStatus = document.getElementById('call-status');
        const chatMessages = document.getElementById('chat-messages');
        const audioIndicator = document.getElementById('audio-indicator');
        const voiceIndicator = document.getElementById('voice-indicator');
        let audioLevel = 0;
        let animationFrame;
        let audioContext, analyser, audioSource;
        let callStartTime = null;
        let callTimerInterval = null;

        // Audio pause control variables
        let audioPauseControl = {
            isEnabled: true,
            isSpeaking: false,
            speechStartTime: 0,
            speechEndTime: 0,
            minSpeechDuration: 100, // milliseconds before pausing
            resumeDelay: 300, // milliseconds to wait after speech ends before resuming
            voiceActivityThreshold: 0.15, // Threshold for voice detection (0-1)
            voiceActivitySamples: [],
            voiceActivitySampleSize: 10,
            pausedPosition: 0,
            wasPlaying: false,
            resumeTimeout: null,
            // Debounce settings to prevent rapid pause/resume
            lastStateChange: 0,
            minTimeBetweenChanges: 200 // milliseconds
        };
        
        // Audio playback monitoring variables
        let audioPlaybackMonitor = {
            isMonitoring: false,
            audioElement: null,
            audioContext: null,
            sourceNode: null,
            analyserNode: null,
            lastPlayTime: 0,
            lastBufferTime: 0,
            bufferUnderrunCount: 0,
            totalBufferChecks: 0,
            stallCount: 0,
            lastCurrentTime: 0,
            playbackGaps: [],
            expectedPlaybackRate: 1.0,
            statusUpdateInterval: null,
            bufferCheckInterval: null,
            silenceThreshold: 0.001,
            maxSilenceDuration: 2000, // 2 seconds for received audio
            lastAudioActivity: 0,
            audioDropThreshold: 0.05 // 5% drop threshold
        };
        
        // Audio pause/resume functions
        function handleVoiceActivity(isUserSpeaking) {
            if (!audioPauseControl.isEnabled || !audioOutput) return;

            const now = Date.now();

            // Debounce rapid state changes
            if (now - audioPauseControl.lastStateChange < audioPauseControl.minTimeBetweenChanges) {
                return;
            }

            if (isUserSpeaking && !audioPauseControl.isSpeaking) {
                // User started speaking
                audioPauseControl.isSpeaking = true;
                audioPauseControl.speechStartTime = now;
                audioPauseControl.lastStateChange = now;
                console.log('User started speaking - pausing audio');

                // Cancel any pending resume
                if (audioPauseControl.resumeTimeout) {
                    clearTimeout(audioPauseControl.resumeTimeout);
                    audioPauseControl.resumeTimeout = null;
                }

                // Wait for minimum speech duration before pausing
                setTimeout(() => {
                    if (audioPauseControl.isSpeaking) {
                        pauseAudioPlayback();
                    }
                }, audioPauseControl.minSpeechDuration);

            } else if (!isUserSpeaking && audioPauseControl.isSpeaking) {
                // User stopped speaking
                audioPauseControl.isSpeaking = false;
                audioPauseControl.speechEndTime = now;
                audioPauseControl.lastStateChange = now;
                console.log('User stopped speaking - scheduling audio resume');

                // Hide voice indicator
                voiceIndicator.classList.remove('active');

                // Resume playback after a delay
                audioPauseControl.resumeTimeout = setTimeout(() => {
                    if (!audioPauseControl.isSpeaking) {
                        resumeAudioPlayback();
                    }
                }, audioPauseControl.resumeDelay);
            }
        }

        function pauseAudioPlayback() {
            if (!audioOutput || audioOutput.paused) return;

            audioPauseControl.wasPlaying = !audioOutput.paused;
            audioPauseControl.pausedPosition = audioOutput.currentTime;

            audioOutput.pause();
            voiceIndicator.classList.add('active');
            console.log('Audio playback paused at position:', audioPauseControl.pausedPosition);
        }

        function resumeAudioPlayback() {
            if (!audioOutput || !audioPauseControl.wasPlaying) return;

            // Try to resume from where we paused
            if (audioPauseControl.pausedPosition > 0) {
                audioOutput.currentTime = audioPauseControl.pausedPosition;
            }

            audioOutput.play().then(() => {
                console.log('Audio playback resumed from position:', audioPauseControl.pausedPosition);
                audioPauseControl.pausedPosition = 0;
                audioPauseControl.wasPlaying = false;
            }).catch(err => {
                console.warn('Failed to resume audio playback:', err);
                // Try to play without seeking
                audioOutput.play().catch(e => console.error('Audio play failed:', e));
            });
        }

        function detectVoiceActivity(audioLevel) {
            // Add current audio level to samples
            audioPauseControl.voiceActivitySamples.push(audioLevel);

            // Keep only recent samples
            if (audioPauseControl.voiceActivitySamples.length > audioPauseControl.voiceActivitySampleSize) {
                audioPauseControl.voiceActivitySamples.shift();
            }

            // Calculate average audio level
            const avgLevel = audioPauseControl.voiceActivitySamples.reduce((a, b) => a + b, 0) /
                           audioPauseControl.voiceActivitySamples.length;

            // Detect voice activity with hysteresis
            const threshold = audioPauseControl.isSpeaking ?
                audioPauseControl.voiceActivityThreshold * 0.8 : // Lower threshold to maintain speaking state
                audioPauseControl.voiceActivityThreshold;

            const isVoiceActive = avgLevel > threshold;

            return isVoiceActive;
        }

        // Add ripple effect to button
        phoneButton.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            ripple.classList.add('ripple');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = e.clientX - rect.left - size / 2 + 'px';
            ripple.style.top = e.clientY - rect.top - size / 2 + 'px';
            this.appendChild(ripple);
            setTimeout(() => ripple.remove(), 600);
        });

        function updateButtonState() {
            if (peerConnection && (peerConnection.connectionState === 'connecting' || peerConnection.connectionState === 'new')) {
                phoneButton.className = 'phone-button connecting';
                phoneButton.disabled = true;
                phoneIcon.textContent = '📞';
                callStatus.textContent = 'Connecting...';
                audioIndicator.classList.remove('active');
            } else if (peerConnection && peerConnection.connectionState === 'connected') {
                phoneButton.className = 'phone-button hangup';
                phoneButton.disabled = false;
                startCallTimer();
                audioIndicator.classList.add('active');
            } else {
                phoneButton.className = 'phone-button call';
                phoneButton.disabled = false;
                phoneIcon.textContent = '📞';
                callStatus.textContent = '';
                stopCallTimer();
                audioIndicator.classList.remove('active');
            }
        }

        function startCallTimer() {
            callStartTime = Date.now();
            callTimerInterval = setInterval(updateCallTimer, 1000);
        }

        function stopCallTimer() {
            if (callTimerInterval) {
                clearInterval(callTimerInterval);
                callTimerInterval = null;
            }
            callStartTime = null;
        }

        function updateCallTimer() {
            if (!callStartTime) return;
            const duration = Math.floor((Date.now() - callStartTime) / 1000);
            const minutes = Math.floor(duration / 60);
            const seconds = duration % 60;
            callStatus.innerHTML = `<span class="call-timer">${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}</span>`;
        }
        
        function startAudioPlaybackMonitoring() {
            const audioElement = document.getElementById('audio-output');
            audioPlaybackMonitor.audioElement = audioElement;
            audioPlaybackMonitor.isMonitoring = true;
            audioPlaybackMonitor.lastPlayTime = performance.now();
            audioPlaybackMonitor.lastAudioActivity = Date.now();
            
            // Setup audio context for playback analysis
            if (!audioPlaybackMonitor.audioContext && audioElement.srcObject) {
                try {
                    audioPlaybackMonitor.audioContext = new (window.AudioContext || window.webkitAudioContext)();
                    audioPlaybackMonitor.sourceNode = audioPlaybackMonitor.audioContext.createMediaStreamSource(audioElement.srcObject);
                    audioPlaybackMonitor.analyserNode = audioPlaybackMonitor.audioContext.createAnalyser();
                    audioPlaybackMonitor.analyserNode.fftSize = 256;
                    audioPlaybackMonitor.sourceNode.connect(audioPlaybackMonitor.analyserNode);
                } catch (e) {
                    console.warn('Could not setup playback audio context:', e);
                }
            }
            
            // Monitor audio element events
            audioElement.addEventListener('stalled', handleAudioStall);
            audioElement.addEventListener('waiting', handleAudioWaiting);
            audioElement.addEventListener('canplay', handleAudioCanPlay);
            audioElement.addEventListener('timeupdate', handleTimeUpdate);
            
            // Start monitoring intervals
            audioPlaybackMonitor.statusUpdateInterval = setInterval(updatePlaybackStatus, 1000);
            audioPlaybackMonitor.bufferCheckInterval = setInterval(checkAudioBuffer, 100);
            
            document.getElementById('audio-status').style.display = 'block';
            console.log('Started audio playback monitoring');
        }
        
        function stopAudioPlaybackMonitoring() {
            audioPlaybackMonitor.isMonitoring = false;
            
            if (audioPlaybackMonitor.statusUpdateInterval) {
                clearInterval(audioPlaybackMonitor.statusUpdateInterval);
                audioPlaybackMonitor.statusUpdateInterval = null;
            }
            
            if (audioPlaybackMonitor.bufferCheckInterval) {
                clearInterval(audioPlaybackMonitor.bufferCheckInterval);
                audioPlaybackMonitor.bufferCheckInterval = null;
            }
            
            const audioElement = audioPlaybackMonitor.audioElement;
            if (audioElement) {
                audioElement.removeEventListener('stalled', handleAudioStall);
                audioElement.removeEventListener('waiting', handleAudioWaiting);
                audioElement.removeEventListener('canplay', handleAudioCanPlay);
                audioElement.removeEventListener('timeupdate', handleTimeUpdate);
            }
            
            if (audioPlaybackMonitor.audioContext) {
                audioPlaybackMonitor.audioContext.close();
                audioPlaybackMonitor.audioContext = null;
            }
            
            document.getElementById('audio-status').style.display = 'none';
            
            // Reset counters
            audioPlaybackMonitor.bufferUnderrunCount = 0;
            audioPlaybackMonitor.totalBufferChecks = 0;
            audioPlaybackMonitor.stallCount = 0;
            audioPlaybackMonitor.playbackGaps = [];
            
            console.log('Stopped audio playback monitoring');
        }
        
        function handleAudioStall(event) {
            audioPlaybackMonitor.stallCount++;
            console.warn('Audio stalled:', event);
            showToast('Audio playback stalled', 'warning');
        }
        
        function handleAudioWaiting(event) {
            console.warn('Audio waiting for data:', event);
            showToast('Waiting for audio data', 'info');
        }
        
        function handleAudioCanPlay(event) {
            console.log('Audio can play again');
        }
        
        function handleTimeUpdate(event) {
            const currentTime = event.target.currentTime;
            const now = performance.now();
            
            // Detect playback gaps
            if (audioPlaybackMonitor.lastCurrentTime > 0) {
                const expectedTimeDiff = (now - audioPlaybackMonitor.lastPlayTime) / 1000;
                const actualTimeDiff = currentTime - audioPlaybackMonitor.lastCurrentTime;
                const gap = Math.abs(expectedTimeDiff - actualTimeDiff);
                
                if (gap > 0.1) { // 100ms gap threshold
                    audioPlaybackMonitor.playbackGaps.push({
                        time: now,
                        gap: gap
                    });
                    
                    // Keep only recent gaps (last 30 seconds)
                    audioPlaybackMonitor.playbackGaps = audioPlaybackMonitor.playbackGaps.filter(
                        g => now - g.time < 30000
                    );
                }
            }
            
            audioPlaybackMonitor.lastCurrentTime = currentTime;
            audioPlaybackMonitor.lastPlayTime = now;
        }
        
        function checkAudioBuffer() {
            if (!audioPlaybackMonitor.isMonitoring) return;
            
            const audioElement = audioPlaybackMonitor.audioElement;
            if (!audioElement) return;
            
            audioPlaybackMonitor.totalBufferChecks++;
            
            // Check buffer health
            if (audioElement.buffered && audioElement.buffered.length > 0) {
                const bufferedEnd = audioElement.buffered.end(audioElement.buffered.length - 1);
                const currentTime = audioElement.currentTime;
                const bufferAhead = bufferedEnd - currentTime;
                
                // Buffer underrun detection
                if (bufferAhead < 0.1) { // Less than 100ms buffered ahead
                    audioPlaybackMonitor.bufferUnderrunCount++;
                }
            }
            
            // Check received audio activity
            if (audioPlaybackMonitor.analyserNode) {
                const dataArray = new Uint8Array(audioPlaybackMonitor.analyserNode.frequencyBinCount);
                audioPlaybackMonitor.analyserNode.getByteFrequencyData(dataArray);
                const average = Array.from(dataArray).reduce((a, b) => a + b, 0) / dataArray.length;
                const audioLevel = average / 255;
                
                if (audioLevel > audioPlaybackMonitor.silenceThreshold) {
                    audioPlaybackMonitor.lastAudioActivity = Date.now();
                }
            }
        }
        
        function updatePlaybackStatus() {
            if (!audioPlaybackMonitor.isMonitoring) return;
            
            const bufferUnderrunRate = audioPlaybackMonitor.totalBufferChecks > 0 ? 
                (audioPlaybackMonitor.bufferUnderrunCount / audioPlaybackMonitor.totalBufferChecks) : 0;
            
            const recentGaps = audioPlaybackMonitor.playbackGaps.filter(
                g => performance.now() - g.time < 10000 // Last 10 seconds
            );
            
            const avgGap = recentGaps.length > 0 ? 
                recentGaps.reduce((sum, g) => sum + g.gap, 0) / recentGaps.length : 0;
            
            const statusElement = document.getElementById('audio-status');
            let statusText = '';
            let statusClass = '';
            
            // Determine status based on multiple factors
            if (bufferUnderrunRate > audioPlaybackMonitor.audioDropThreshold || 
                audioPlaybackMonitor.stallCount > 0 || 
                avgGap > 0.2) {
                statusText = `Playback: Poor (${(bufferUnderrunRate * 100).toFixed(1)}% drops, ${recentGaps.length} gaps)`;
                statusClass = 'bad';
                if (bufferUnderrunRate > audioPlaybackMonitor.audioDropThreshold) {
                    showToast(`High playback drop rate: ${(bufferUnderrunRate * 100).toFixed(1)}%`, 'warning');
                }
            } else if (bufferUnderrunRate > audioPlaybackMonitor.audioDropThreshold / 2 || avgGap > 0.1) {
                statusText = `Playback: Fair (${(bufferUnderrunRate * 100).toFixed(1)}% drops, ${recentGaps.length} gaps)`;
                statusClass = 'poor';
            } else {
                statusText = `Playback: Good (${(bufferUnderrunRate * 100).toFixed(1)}% drops)`;
                statusClass = 'good';
            }
            
            // Check for silence from server
            const currentTime = Date.now();
            const silenceDuration = currentTime - audioPlaybackMonitor.lastAudioActivity;
            if (silenceDuration > audioPlaybackMonitor.maxSilenceDuration) {
                statusText += ' - No server audio';
                statusClass = 'poor';
            }
            
            statusElement.textContent = statusText;
            statusElement.className = `audio-status ${statusClass}`;
        }
        
        function setupAudioVisualization(stream) {
            audioContext = new (window.AudioContext || window.webkitAudioContext)();
            analyser = audioContext.createAnalyser();
            audioSource = audioContext.createMediaStreamSource(stream);
            audioSource.connect(analyser);
            analyser.fftSize = 256;
            analyser.smoothingTimeConstant = 0.3;
            const dataArray = new Uint8Array(analyser.frequencyBinCount);

            function updateAudioLevel() {
                analyser.getByteFrequencyData(dataArray);
                const average = Array.from(dataArray).reduce((a, b) => a + b, 0) / dataArray.length;
                audioLevel = average / 255;

                // Apply smoothing and amplification for better visualization
                const smoothedLevel = Math.min(audioLevel * 2, 1);

                // Update both audio indicator and mic visualization
                audioIndicator.style.setProperty('--audio-level', 1 + smoothedLevel * 0.3);

                // Update mic container audio level for ring animations
                const micContainer = document.getElementById('mic-container');
                if (micContainer) {
                    micContainer.style.setProperty('--audio-level', smoothedLevel);

                    // Also update individual rings for better control
                    const rings = micContainer.querySelectorAll('.audio-ring');
                    rings.forEach((ring, index) => {
                        ring.style.setProperty('--audio-level', smoothedLevel);
                    });
                }

                // Detect voice activity and handle pause/resume
                const isUserSpeaking = detectVoiceActivity(smoothedLevel);
                handleVoiceActivity(isUserSpeaking);

                animationFrame = requestAnimationFrame(updateAudioLevel);
            }
            updateAudioLevel();
        }
        
        function showError(message) {
            showToast(message, 'error');
        }
        
        function showToast(message, type = 'info') {
            const toast = document.getElementById('error-toast');
            toast.textContent = message;
            toast.className = `toast ${type}`;
            toast.style.display = 'block';

            // Hide toast after 5 seconds
            setTimeout(() => {
                toast.style.display = 'none';
            }, 5000);
        }

        // 音量控制函数
        function reduceVolume(targetVolume = 0.3) {
            const audioElement = document.getElementById('audio-output');
            if (audioElement) {
                // 保存当前音量作为原始音量（如果还没有保存的话）
                if (!volumeControl.isReduced) {
                    volumeControl.originalVolume = audioElement.volume;
                }

                // 设置新音量
                audioElement.volume = targetVolume;
                volumeControl.currentVolume = targetVolume;
                volumeControl.isReduced = true;

                console.log(`🔉 音量已降低到 ${Math.round(targetVolume * 100)}%`);
                showToast(`音量已降低到 ${Math.round(targetVolume * 100)}%`, 'info');
            }
        }

        function restoreVolume() {
            const audioElement = document.getElementById('audio-output');
            if (audioElement && volumeControl.isReduced) {
                // 恢复到原始音量
                audioElement.volume = volumeControl.originalVolume;
                volumeControl.currentVolume = volumeControl.originalVolume;
                volumeControl.isReduced = false;

                console.log(`🔊 音量已恢复到 ${Math.round(volumeControl.originalVolume * 100)}%`);
                showToast(`音量已恢复到 ${Math.round(volumeControl.originalVolume * 100)}%`, 'info');
            }
        }

        function handleVolumeControl(eventData) {
            console.log('处理音量控制事件:', eventData);

            if (!eventData.action) {
                console.warn('音量控制事件缺少action字段');
                return;
            }

            switch (eventData.action) {
                case 'reduce':
                    const targetVolume = eventData.volume || 0.3;
                    reduceVolume(targetVolume);
                    break;

                case 'restore':
                    restoreVolume();
                    break;

                default:
                    console.warn('未知的音量控制动作:', eventData.action);
            }
        }
        
        async function setupWebRTC() {
            // 优化音频约束
            const audioConstraints = {
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true,
                    sampleRate: 24000,
                    channelCount: 1,
                    sampleSize: 16
                }
            };
            
            isConnecting = true;
            const config = __RTC_CONFIGURATION__;
            peerConnection = new RTCPeerConnection(config);
            
            const timeoutId = setTimeout(() => {
                showToast("Connection is taking longer than usual. Are you on a VPN?", 'warning');
            }, 5000);
            
            try {
                const stream = await navigator.mediaDevices.getUserMedia(audioConstraints);
                setupAudioVisualization(stream);
                
                stream.getTracks().forEach(track => {
                    peerConnection.addTrack(track, stream);
                });
                
                peerConnection.addEventListener('track', (evt) => {
                    if (audioOutput.srcObject !== evt.streams[0]) {
                        audioOutput.srcObject = evt.streams[0];
                        audioOutput.play();
                        
                        // Start monitoring received audio playback
                        setTimeout(() => {
                            startAudioPlaybackMonitoring();
                        }, 1000); // Wait a bit for audio to start
                    }
                });
                
                // Monitor RTC connection stats
                const statsInterval = setInterval(async () => {
                    if (peerConnection && peerConnection.connectionState === 'connected') {
                        try {
                            const stats = await peerConnection.getStats();
                            stats.forEach((report) => {
                                if (report.type === 'inbound-rtp' && report.mediaType === 'audio') {
                                    // Monitor packet loss and jitter
                                    if (report.packetsLost > 0) {
                                        console.warn('Audio packets lost:', report.packetsLost);
                                    }
                                    if (report.jitter > 0.03) { // 30ms jitter threshold
                                        console.warn('High audio jitter detected:', report.jitter);
                                    }
                                }
                            });
                        } catch (e) {
                            console.error('Error getting RTC stats:', e);
                        }
                    } else {
                        clearInterval(statsInterval);
                    }
                }, 5000);
                
                const dataChannel = peerConnection.createDataChannel('text');
                dataChannel.onmessage = (event) => {
                    const eventJson = JSON.parse(event.data);
                    if (eventJson.type === "error") {
                        showError(eventJson.message);
                    }
                };
                
                const offer = await peerConnection.createOffer();
                await peerConnection.setLocalDescription(offer);
                
                await new Promise((resolve) => {
                    if (peerConnection.iceGatheringState === "complete") {
                        resolve();
                    } else {
                        const checkState = () => {
                            if (peerConnection.iceGatheringState === "complete") {
                                peerConnection.removeEventListener("icegatheringstatechange", checkState);
                                resolve();
                            }
                        };
                        peerConnection.addEventListener("icegatheringstatechange", checkState);
                    }
                });
                
                peerConnection.addEventListener('connectionstatechange', () => {
                    console.log('connectionstatechange', peerConnection.connectionState);
                    if (peerConnection.connectionState === 'connected') {
                        clearTimeout(timeoutId);
                        const toast = document.getElementById('error-toast');
                        toast.style.display = 'none';
                        showToast('Connected - monitoring playback quality', 'info');
                    } else if (peerConnection.connectionState === 'disconnected' || 
                              peerConnection.connectionState === 'failed') {
                        showToast('Connection lost - playback monitoring stopped', 'error');
                        stopAudioPlaybackMonitoring();
                    }
                    updateButtonState();
                });
                
                webrtc_id = Math.random().toString(36).substring(7);
                const response = await fetch('/webrtc/offer', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        sdp: peerConnection.localDescription.sdp,
                        type: peerConnection.localDescription.type,
                        webrtc_id: webrtc_id
                    })
                });
                
                const serverResponse = await response.json();
                if (serverResponse.status === 'failed') {
                    showError(serverResponse.meta.error === 'concurrency_limit_reached'
                        ? `Too many connections. Maximum limit is ${serverResponse.meta.limit}`
                        : serverResponse.meta.error);
                    stop();
                    return;
                }
                
                await peerConnection.setRemoteDescription(serverResponse);
                
                const eventSource = new EventSource('/outputs?webrtc_id=' + webrtc_id);
                eventSource.addEventListener("output", (event) => {
                    const eventJson = JSON.parse(event.data);
                    console.log("eventJson", eventJson)

                    // Handle interruption event
                    if (eventJson.type === "[interruption]") {
                        console.log("Interruption received");
                        // Stop audio playback
                        if (audioOutput) {
                            audioOutput.pause();
                            audioOutput.currentTime = 0;
                        }
                        // Show interruption message
                        showToast(eventJson.content, "warning");
                        return;
                    } else if (eventJson.type === "[start]") {
                        console.log("start received");
                        audioOutput.play();
                        // Show interruption message
                        showToast(eventJson.content, "warning");
                        return;
                    } else if (eventJson.type === "search_results") {
                        console.log("Search results received", eventJson);
                        // 处理搜索结果显示 - 只在右边商品区域显示，不在聊天框显示
                        if (eventJson.raw_data && Array.isArray(eventJson.raw_data)) {
                            // 从消息内容中提取查询词
                            const queryMatch = eventJson.content.match(/「(.+?)」/);
                            const query = queryMatch ? queryMatch[1] : '搜索';
                            displayProducts(eventJson.raw_data, query);
                        }
                        // 不在对话框中显示搜索结果，只在右边显示
                        return;
                    } else if (eventJson.type === "product_control") {
                        console.log("Product control received", eventJson);
                        // 处理商品控制事件
                        handleProductControl(eventJson);
                        return;
                    } else if (eventJson.type === "volume_control") {
                        console.log("Volume control received", eventJson);
                        // 处理音量控制事件
                        handleVolumeControl(eventJson);
                        return;
                    }

                    var element = document.getElementById(eventJson.id);
                    if (element) {
                        return element.textContent += eventJson.content
                    } else {
                        addMessage("assistant", eventJson.content, eventJson.id);
                    }
                });

                // Add interruption event listener
                eventSource.addEventListener("interruption", (event) => {
                    console.log("Interruption received");
                    // Stop audio playback
                    if (audioOutput) {
                        audioOutput.pause();
                        audioOutput.currentTime = 0;
                    }
                    // Show interruption message
                    showToast("Conversation interrupted", "warning");
                });
                
            } catch (err) {
                clearTimeout(timeoutId);
                console.error('Error setting up WebRTC:', err);
                showError('Failed to establish connection. Please try again.');
                stop();
            }
        }
        
        function addMessage(role, content, id) {
            const messageWrapper = document.createElement('div');
            messageWrapper.classList.add('message-wrapper', role);

            const messageDiv = document.createElement('div');
            messageDiv.classList.add('message', role);

            // 检查内容是否包含HTML标签
            if (content.includes('<div') || content.includes('<h3')) {
                messageDiv.innerHTML = content;
            } else {
                messageDiv.textContent = content;
            }

            messageDiv.id = id;

            messageWrapper.appendChild(messageDiv);
            chatMessages.appendChild(messageWrapper);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 商品轮播相关变量
        let currentProducts = [];
        let currentProductIndex = 0;

        function displayProducts(products, query) {
            if (!products || products.length === 0) {
                showNoProducts(query);
                return;
            }

            // 保存商品数据
            currentProducts = products;
            currentProductIndex = 0;

            // 更新标题
            const productHeader = document.querySelector('.product-header h3');
            productHeader.textContent = `🛍️ 「${query || '搜索'}」结果 (${products.length}个商品)`;

            // 隐藏无商品提示，显示商品轮播
            document.getElementById('no-products').style.display = 'none';
            document.getElementById('product-display').style.display = 'block';

            // 渲染当前商品和指示器
            renderCurrentProduct();
            renderIndicators();
        }

        function showNoProducts(query) {
            document.getElementById('no-products').style.display = 'block';
            document.getElementById('product-display').style.display = 'none';
            document.getElementById('no-products').innerHTML = `
                未找到「${query || ''}」相关商品<br>
                请尝试其他关键词
            `;
        }

        function renderCurrentProduct() {
            if (!currentProducts || currentProducts.length === 0) return;

            const product = currentProducts[currentProductIndex];

            // 适配不同的字段名
            const title = product.itemTitle || product.title || '商品标题';
            const price = product.itemPrice || product.price || '价格待询';
            const image = product.pic_url || product.image || '';
            const itemId = product.itemId || product.nid || product.id || '';
            const shopName = product.seller_name || product.shop_name || product.shop || '';
            const sales = product.transNum30d || product.sales || '';
            const bcType = product.bcType || '';

            // 清理价格格式
            let formattedPrice = price;
            if (typeof price === 'number') {
                formattedPrice = price.toFixed(2);
            } else if (typeof price === 'string') {
                formattedPrice = price.replace('¥', '').trim();
            }

            // 更新商品显示
            document.getElementById('product-image').src = image || '';
            document.getElementById('product-image').alt = title;
            document.getElementById('product-title').textContent = title;
            document.getElementById('product-price').textContent = formattedPrice;

            // 构建商品元信息
            let metaInfo = [];
            if (shopName) metaInfo.push(`🏪 ${shopName}`);
            if (sales) metaInfo.push(`📈 月销${sales}`);
            if (bcType) metaInfo.push(`🏷️ ${bcType}`);
            metaInfo.push(`ID: ${itemId}`);

            document.getElementById('product-meta').innerHTML = metaInfo.join('<br>');

            // 设置选择按钮的商品ID
            document.getElementById('select-button').setAttribute('data-item-id', itemId);

            // 更新导航按钮状态
            document.getElementById('prev-button').disabled = currentProductIndex === 0;
            document.getElementById('next-button').disabled = currentProductIndex === currentProducts.length - 1;

            console.log(`显示商品 ${currentProductIndex + 1}/${currentProducts.length}: ${title}`);
        }

        function renderIndicators() {
            const indicatorContainer = document.getElementById('product-indicator');
            indicatorContainer.innerHTML = '';

            if (!currentProducts || currentProducts.length <= 1) {
                return; // 只有一个商品时不显示指示器
            }

            for (let i = 0; i < currentProducts.length; i++) {
                const dot = document.createElement('div');
                dot.className = `indicator-dot ${i === currentProductIndex ? 'active' : ''}`;
                dot.onclick = () => goToProduct(i);
                indicatorContainer.appendChild(dot);
            }
        }

        function showNextProduct() {
            if (!currentProducts || currentProducts.length === 0) return;

            if (currentProductIndex < currentProducts.length - 1) {
                currentProductIndex++;
                renderCurrentProduct();
                renderIndicators();
                console.log('右滑到下一个商品');
            }
        }

        function showPreviousProduct() {
            if (!currentProducts || currentProducts.length === 0) return;

            if (currentProductIndex > 0) {
                currentProductIndex--;
                renderCurrentProduct();
                renderIndicators();
                console.log('左滑到上一个商品');
            }
        }

        function goToProduct(index) {
            if (!currentProducts || index < 0 || index >= currentProducts.length) return;

            currentProductIndex = index;
            renderCurrentProduct();
            renderIndicators();
            console.log(`跳转到商品 ${index + 1}`);
        }

        function selectCurrentProduct() {
            if (!currentProducts || currentProducts.length === 0) return;

            const product = currentProducts[currentProductIndex];
            const itemId = product.itemId || product.nid || product.id || '';
            const title = product.itemTitle || product.title || '商品标题';

            console.log(`选择商品: ${title} (ID: ${itemId})`);

            // 这里可以添加选择商品的逻辑，比如发送到后端
            showToast(`已选择商品: ${title}`, 'info');
        }

        function handleProductControl(eventData) {
            console.log('处理商品控制事件:', eventData);

            if (!eventData.action) {
                console.warn('商品控制事件缺少action字段');
                return;
            }

            // 如果后端提供了product_index，直接同步到前端
            if (typeof eventData.product_index === 'number' && currentProducts && currentProducts.length > 0) {
                if (eventData.product_index >= 0 && eventData.product_index < currentProducts.length) {
                    currentProductIndex = eventData.product_index;
                    console.log(`同步商品索引到: ${currentProductIndex}`);
                }
            }

            switch (eventData.action) {
                case 'prev':
                    // 语音控制左滑 - 直接更新显示，不再调用showPreviousProduct避免重复操作
                    if (currentProducts && currentProducts.length > 0) {
                        renderCurrentProduct();
                        renderIndicators();
                        showToast('已切换到上一个商品', 'info');
                        console.log(`语音左滑到商品 ${currentProductIndex + 1}/${currentProducts.length}`);
                    }
                    break;

                case 'next':
                    // 语音控制右滑 - 直接更新显示，不再调用showNextProduct避免重复操作
                    if (currentProducts && currentProducts.length > 0) {
                        renderCurrentProduct();
                        renderIndicators();
                        showToast('已切换到下一个商品', 'info');
                        console.log(`语音右滑到商品 ${currentProductIndex + 1}/${currentProducts.length}`);
                    }
                    break;

                case 'select':
                    // 语音控制选择商品
                    if (currentProducts && currentProducts.length > 0) {
                        const product = currentProducts[currentProductIndex];
                        const title = product.itemTitle || product.title || '商品标题';
                        showToast(`已选择商品: ${title}`, 'info');

                        // 可以在这里添加选择商品后的处理逻辑
                        console.log('语音选择商品:', product);
                    }
                    break;

                default:
                    console.warn('未知的商品控制动作:', eventData.action);
            }
        }
        
        function stop() {
            if (animationFrame) {
                cancelAnimationFrame(animationFrame);
            }

            stopAudioPlaybackMonitoring();

            // Reset audio pause control
            audioPauseControl.isSpeaking = false;
            audioPauseControl.wasPlaying = false;
            audioPauseControl.pausedPosition = 0;
            if (audioPauseControl.resumeTimeout) {
                clearTimeout(audioPauseControl.resumeTimeout);
                audioPauseControl.resumeTimeout = null;
            }
            voiceIndicator.classList.remove('active');

            if (audioContext) {
                audioContext.close();
                audioContext = null;
                analyser = null;
                audioSource = null;
            }
            if (peerConnection) {
                if (peerConnection.getTransceivers) {
                    peerConnection.getTransceivers().forEach(transceiver => {
                        if (transceiver.stop) {
                            transceiver.stop();
                        }
                    });
                }
                if (peerConnection.getSenders) {
                    peerConnection.getSenders().forEach(sender => {
                        if (sender.track && sender.track.stop) sender.track.stop();
                    });
                }
                console.log('closing');
                peerConnection.close();
            }
            updateButtonState();
            audioLevel = 0;
        }

        phoneButton.addEventListener('click', () => {
            console.log('clicked');
            console.log(peerConnection, peerConnection?.connectionState);
            if (!peerConnection || peerConnection.connectionState !== 'connected') {
                setupWebRTC();
            } else {
                console.log('hanging up');
                stop();
                showToast('Call ended', 'info');
            }
        });
    </script>
</body>

</html>