[mypy]
pretty = True
show_error_codes = True

# Exclude _files.py and _logs.py because mypy isn't smart enough to apply
# the correct type narrowing and as this is an internal module
# it's fine to just use Pyright.
#
# We also exclude our `tests` as mypy doesn't always infer
# types correctly and <PERSON>yr<PERSON> will still catch any type errors.

# realtime examples use inline `uv` script dependencies
# which means it can't be type checked
exclude = ^(src/openai/_files\.py|_dev/.*\.py|tests/.*|src/openai/_utils/_logs\.py|examples/realtime/audio_util\.py|examples/realtime/push_to_talk_app\.py)$

strict_equality = True
implicit_reexport = True
check_untyped_defs = True
no_implicit_optional = True

warn_return_any = True
warn_unreachable = True
warn_unused_configs = True

# Turn these options off as it could cause conflicts
# with the Pyright options.
warn_unused_ignores = False
warn_redundant_casts = False

disallow_any_generics = True
disallow_untyped_defs = True
disallow_untyped_calls = True
disallow_subclassing_any = True
disallow_incomplete_defs = True
disallow_untyped_decorators = True
cache_fine_grained = True

# By default, mypy reports an error if you assign a value to the result
# of a function call that doesn't return anything. We do this in our test
# cases:
# ```
# result = ...
# assert result is None
# ```
# Changing this codegen to make mypy happy would increase complexity
# and would not be worth it.
disable_error_code = func-returns-value,overload-cannot-match

# https://github.com/python/mypy/issues/12162
[mypy.overrides]
module = "black.files.*"
ignore_errors = true
ignore_missing_imports = true
