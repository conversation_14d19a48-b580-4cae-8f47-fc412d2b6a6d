# generated by rye
# use `rye lock` or `rye sync` to update this lockfile
#
# last locked with the following flags:
#   pre: false
#   features: []
#   all-features: true
#   with-sources: false
#   generate-hashes: false
#   universal: false

-e file:.
annotated-types==0.6.0
    # via pydantic
anyio==4.1.0
    # via httpx
    # via openai
certifi==2023.7.22
    # via httpcore
    # via httpx
cffi==1.17.1
    # via sounddevice
distro==1.8.0
    # via openai
exceptiongroup==1.2.2
    # via anyio
h11==0.14.0
    # via httpcore
httpcore==1.0.2
    # via httpx
httpx==0.28.1
    # via openai
idna==3.4
    # via anyio
    # via httpx
jiter==0.6.1
    # via openai
numpy==2.0.2
    # via openai
    # via pandas
    # via pandas-stubs
pandas==2.2.3
    # via openai
pandas-stubs==2.2.2.240807
    # via openai
pycparser==2.22
    # via cffi
pydantic==2.10.3
    # via openai
pydantic-core==2.27.1
    # via pydantic
python-dateutil==2.9.0.post0
    # via pandas
pytz==2024.1
    # via pandas
six==1.16.0
    # via python-dateutil
sniffio==1.3.0
    # via anyio
    # via openai
sounddevice==0.5.1
    # via openai
tqdm==4.66.5
    # via openai
types-pytz==2024.2.0.20241003
    # via pandas-stubs
typing-extensions==4.12.2
    # via openai
    # via pydantic
    # via pydantic-core
tzdata==2024.1
    # via pandas
websockets==15.0.1
    # via openai
