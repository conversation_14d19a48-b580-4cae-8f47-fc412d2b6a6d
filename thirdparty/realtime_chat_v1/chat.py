import os
import asyncio
from openai import AsyncAzureOpenAI
import json
from typing import AsyncGenerator, Optional
import threading
import sys
import select
import termios
import tty
from enum import Enum
from system_prompt import system_prompt
from msg_type import ChatEvent
class LLMState(Enum):
    """LLM状态枚举"""
    IDLE = "idle"           # 空闲状态
    PREPARING = "preparing" # 准备生成
    GENERATING = "generating" # 正在生成
    INTERRUPTED = "interrupted" # 已中断
    ERROR = "error"         # 错误状态


class InterruptibleAzureOpenAI:
    def __init__(self, api_key: str, endpoint: str, api_version: str = "2024-02-15-preview"):
        """
        支持打断功能的Azure OpenAI客户端
        """
        self.client = AsyncAzureOpenAI(
            api_key=api_key,
            azure_endpoint=endpoint,
            api_version=api_version
        )
        init_message = {"role": "system", "content": system_prompt}
        self.conversation_history = [init_message]
        self.current_task: Optional[asyncio.Task] = None
        self.interrupt_event = asyncio.Event()
        
        # 使用asyncio.Lock保护状态变更，确保异步安全
        self._state_lock = asyncio.Lock()
        self._current_state = LLMState.IDLE
        self.generation_start_time = None
        
        # 用于统计的线程安全计数器
        self._generation_count = 0
        self.state_changed = asyncio.Event()

    async def _get_state(self) -> LLMState:
        """
        异步安全地获取当前状态
        
        Returns:
            LLMState: 当前状态
        """
        async with self._state_lock:
            return self._current_state
        
    async def _set_state(self, new_state: LLMState):
        """
        异步安全地设置LLM状态
        
        Args:
            new_state: 新的状态
        """
        async with self._state_lock:
            old_state = self._current_state
            self._current_state = new_state
            self.state_changed.set()  # 通知状态已改变
            self.state_changed.clear()  # 重置事件
            # 状态切换时的特殊处理
            if new_state == LLMState.GENERATING and old_state != LLMState.GENERATING:
                self.generation_start_time = asyncio.get_event_loop().time()
                self._generation_count += 1
            elif new_state != LLMState.GENERATING and old_state == LLMState.GENERATING:
                self.generation_start_time = None
    
    async def _wait_for_state_change_from(self, from_state: LLMState, timeout: float = 5.0):
        """等待状态从指定状态改变"""
        try:
            while await self._get_state() == from_state:
                await asyncio.wait_for(self.state_changed.wait(), timeout=0.1)
        except asyncio.TimeoutError:
            # 定期检查状态，避免错过状态变化
            pass

    async def _is_state(self, state: LLMState) -> bool:
        """
        异步安全地检查是否为指定状态
        
        Args:
            state: 要检查的状态
            
        Returns:
            bool: 是否为指定状态
        """
        current_state = await self._get_state()
        return current_state == state
    
    async def stream_chat_with_interrupt(
        self, 
        message: str, 
        model: str = "gpt-4",
        temperature: float = 0.7,
        max_tokens: int = 1000
    ) -> AsyncGenerator[str, None]:
        """
        可中断的流式对话生成器
        """
        self.conversation_history.append({"role": "user", "content": message})
        

        current_state = await self._get_state()
        if current_state == LLMState.INTERRUPTED:
            print("[等待中断状态清除...]")
            # 等待状态不再是INTERRUPTED，设置超时避免无限等待
            timeout_count = 0
            max_timeout = 50  # 最多等待5秒 (50 * 0.1s)
            
            while await self._get_state() == LLMState.INTERRUPTED and timeout_count < max_timeout:
                await asyncio.sleep(0.1)
                timeout_count += 1
            
            # 检查是否超时
            if await self._get_state() == LLMState.INTERRUPTED:
                print("[超时：强制清除中断状态]")
                await self._set_state(LLMState.IDLE)
            else:
                print("[状态已清除，开始处理新请求]")

        
        try:
            stream = await self.client.chat.completions.create(
                model=model,
                messages=self.conversation_history,
                temperature=temperature,
                max_tokens=max_tokens,
                stream=True
            )
            
            assistant_response = ""
            first_chunk_received = False
            
            async for chunk in stream:
                # 检查是否收到中断信号
                if self.interrupt_event.is_set():
                    await self._set_state(LLMState.INTERRUPTED)
                    self.interrupt_event.clear()
                    print("\n[输出已中断]")
                    yield "[用户已经打断]"
                    break
                
                if chunk.choices[0].delta.content is not None:
                    content = chunk.choices[0].delta.content
                    assistant_response += content
                    
                    # 第一次接收到内容时设置为生成状态
                    if not first_chunk_received:
                        await self._set_state(LLMState.GENERATING)
                        first_chunk_received = True
                        print(f"[LLM开始生成内容]", end=" ", flush=True)
                    
                    yield content
            
            # 根据是否被中断来处理历史记录
            current_state = await self._get_state()
            if current_state != LLMState.INTERRUPTED:
                self.conversation_history.append({"role": "assistant", "content": assistant_response})
                await self._set_state(LLMState.IDLE)
            else:
                # 如果被中断，添加部分回复到历史记录
                if assistant_response:
                    self.conversation_history.append({"role": "assistant", "content": assistant_response + " [已中断]"})
                
        except Exception as e:
            await self._set_state(LLMState.ERROR)
            yield f"错误: {str(e)}"
        finally:
            # 确保最终状态为IDLE（除非是INTERRUPTED状态）
            current_state = await self._get_state()
            await stream.close()
            # if current_state in [LLMState.INTERRUPTED, LLMState.ERROR]:
            await self._set_state(LLMState.IDLE)
    
    async def interrupt(self):
        """
        中断当前的流式输出
        只有在LLM正在生成内容时才会执行中断
        
        Returns:
            bool: 中断是否成功
        """
        current_state = await self._get_state()
        
        if current_state != LLMState.GENERATING and current_state != LLMState.PREPARING:
            print(f"[中断失败] LLM当前状态: {current_state.value}，无法中断")
            return False
        
        # 设置中断事件
        self.interrupt_event.set()
        
        # 计算生成时长
        generation_duration = None
        if self.generation_start_time:
            generation_duration = asyncio.get_event_loop().time() - self.generation_start_time
        
        print(f"\n[正在中断...] 生成时长: {generation_duration:.2f}秒" if generation_duration else "\n[正在中断...]")
        return True
    
    async def get_llm_status(self) -> dict:
        """
        获取LLM当前状态信息
        
        Returns:
            dict: 包含LLM状态的字典
        """
        async with self._state_lock:
            current_state = self._current_state
            current_time = asyncio.get_event_loop().time()
            generation_duration = None
            
            if self.generation_start_time and current_time:
                generation_duration = current_time - self.generation_start_time
            
            return {
                "state": current_state.value,
                "is_generating": current_state == LLMState.GENERATING,
                "can_interrupt": current_state == LLMState.GENERATING,
                "generation_start_time": self.generation_start_time,
                "generation_duration": generation_duration,
                "conversation_length": len(self.conversation_history),
                "total_generations": self._generation_count
            }
    
    async def can_interrupt(self) -> bool:
        """
        检查是否可以中断LLM输出
        
        Returns:
            bool: 如果LLM正在生成内容则返回True，否则返回False
        """
        return await self._is_state(LLMState.GENERATING)
    
    def clear_history(self):
        """清除对话历史"""
        self.conversation_history = []
    
    def get_history(self):
        """获取对话历史"""
        return self.conversation_history.copy()
    
    async def continuous_stream_from_queue_with_interrupt(
        self,
        transcript_queue: asyncio.Queue,
        resp_queue: asyncio.Queue,
        model: str = "gpt-4",
        temperature: float = 0.7,
        max_tokens: int = 1000,
        queue_timeout: float = 0.1,
    ):
        """
        持续从队列中读取消息并进行可中断的流式对话生成
        适用于需要持续监听队列的场景（如实时语音转文字）
        
        Args:
            transcript_queue: 异步队列，包含用户消息
            model: 使用的模型名称
            temperature: 生成温度
            max_tokens: 最大生成token数
            queue_timeout: 单次从队列获取消息的超时时间（秒）
            idle_timeout: 空闲超时时间，超过此时间没有新消息则结束（秒）
            
        Yields:
            str: 生成的文本片段或状态信息
        """
        messages_to_process = []
        while True:
            try:
                # 尝试从队列获取消息
                try:
                    while True:
                        try:
                            message = await asyncio.wait_for(
                                transcript_queue.get(), 
                                timeout=0.1  # 短超时以快速检查队列
                            )
                            messages_to_process.append(message)
                        except asyncio.TimeoutError:
                            # 队列暂时为空，如果已有消息则开始处理
                            if messages_to_process:
                                break
                            # 如果还没有消息，使用长超时等待第一条消息
                            try:
                                message = await asyncio.wait_for(
                                    transcript_queue.get(), 
                                    timeout=queue_timeout
                                )
                                messages_to_process.append(message)
                            except asyncio.TimeoutError:
                                break
                                # print("[队列超时：未收到消息]")
                                # return
                    if len(messages_to_process) <= 0:
                        continue 
                    conv_id = messages_to_process[0].id
                    mtype = messages_to_process[0].type
                    messages_to_process = [msg.content for msg in messages_to_process]
                    combined_message = " ".join(messages_to_process) if len(messages_to_process) > 1 else messages_to_process[0]
                    messages_to_process = []
                    # 生成响应
                    # print(f"\n[处理消息: {combined_message[:50]}...]\n")
                    async for chunk in self.stream_chat_with_interrupt(
                        combined_message, 
                        model=model, 
                        temperature=temperature, 
                        max_tokens=max_tokens
                    ):
                        ce = ChatEvent(conv_id, chunk, mtype)
                        await resp_queue.put(ce)                    
                except asyncio.TimeoutError:
                    # 队列暂时为空，继续等待
                    continue
                    
            except Exception as e:
                print(f"\n[错误: {str(e)}]\n")
                await self._set_state(LLMState.ERROR)
class KeyboardMonitor:
    """键盘监听器，用于检测中断按键"""
    
    def __init__(self, chat_instance):
        self.chat_instance = chat_instance
        self.monitoring = False
        self.thread = None
    
    def start_monitoring(self):
        """开始监听键盘输入"""
        self.monitoring = True
        self.thread = threading.Thread(target=self._monitor_keyboard, daemon=True)
        self.thread.start()
    
    def stop_monitoring(self):
        """停止监听"""
        self.monitoring = False
    
    def _monitor_keyboard(self):
        """在后台线程中监听键盘输入"""
        try:
            # 保存原始终端设置
            old_settings = termios.tcgetattr(sys.stdin)
            tty.setraw(sys.stdin.fileno())
            
            while self.monitoring:
                if select.select([sys.stdin], [], [], 0.1)[0]:
                    key = sys.stdin.read(1)
                    # 检测 Ctrl+C (ASCII 3) 或 ESC (ASCII 27)
                    if ord(key) == 3 or ord(key) == 27:
                        # 只有在LLM正在生成时才执行中断
                        if self.chat_instance.can_interrupt():
                            self.chat_instance.interrupt()
                        else:
                            print(f"\n[提示] LLM当前未在生成内容，无法中断")
                        break
        except:
            pass
        finally:
            # 恢复终端设置
            try:
                termios.tcsetattr(sys.stdin, termios.TCSADRAIN, old_settings)
            except:
                pass

async def interactive_chat_with_interrupt():
    """支持中断的交互式对话"""
    # 配置参数
    API_KEY = os.getenv("AZURE_OPENAI_API_KEY", "your-api-key-here")
    ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT", "your-endpoint-here")
    MODEL_NAME = os.getenv("AZURE_OPENAI_MODEL_NAME", "gpt-4")
    
    chat = InterruptibleAzureOpenAI(api_key=API_KEY, endpoint=ENDPOINT)
    
    print("Azure OpenAI 可中断流式对话 Demo")
    print("输入 'quit' 退出，输入 'clear' 清除历史，输入 'status' 查看LLM状态")
    print("在AI回复过程中按 Ctrl+C 或 ESC 可中断输出")
    print("-" * 60)
    
    while True:
        try:
            # 显示LLM状态
            status = chat.get_llm_status()
            status_indicator = "🟢 就绪" if not status["is_generating"] else "🔴 生成中"
            print(f"\n[LLM状态: {status_indicator}]", end="")
            
            user_input = input(" 你: ").strip()
            
            if user_input.lower() == 'quit':
                print("再见!")
                break
            elif user_input.lower() == 'clear':
                chat.clear_history()
                print("对话历史已清除")
                continue
            elif user_input.lower() == 'status':
                status = chat.get_llm_status()
                print("=" * 40)
                print("LLM 状态信息:")
                print(f"  正在流式输出: {status['is_streaming']}")
                print(f"  正在生成内容: {status['is_generating']}")
                print(f"  可以中断: {status['can_interrupt']}")
                print(f"  对话轮数: {status['conversation_length']}")
                if status['generation_duration']:
                    print(f"  生成时长: {status['generation_duration']:.2f}秒")
                print("=" * 40)
                continue
            elif not user_input:
                continue
            
            print("AI: ", end="", flush=True)
            
            # 创建键盘监听器
            keyboard_monitor = KeyboardMonitor(chat)
            keyboard_monitor.start_monitoring()
            
            try:
                # 流式获取AI回复
                async for chunk in chat.stream_chat_with_interrupt(user_input, model=MODEL_NAME):
                    print(chunk, end="", flush=True)
                    # 添加小延迟使中断更加响应
                    await asyncio.sleep(0.01)
            finally:
                keyboard_monitor.stop_monitoring()
            
            print()  # 换行
            
        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            break
        except Exception as e:
            print(f"\n发生错误: {e}")

# 方法2：使用asyncio.timeout实现自动超时中断
class TimeoutInterruptibleChat:
    """支持超时中断的聊天类"""
    
    def __init__(self, api_key: str, endpoint: str):
        self.chat = InterruptibleAzureOpenAI(api_key, endpoint)
    
    async def chat_with_timeout(self, message: str, timeout: float = 30.0, model: str = "gpt-4"):
        """
        带超时的对话，超时后自动中断
        
        Args:
            message: 用户消息
            timeout: 超时时间（秒）
            model: 模型名称
        """
        try:
            async with asyncio.timeout(timeout):
                async for chunk in self.chat.stream_chat_with_interrupt(message, model=model):
                    print(chunk, end="", flush=True)
                    await asyncio.sleep(0.01)
        except asyncio.TimeoutError:
            print(f"\n[超时中断 - {timeout}秒]")
            if self.chat.can_interrupt():
                self.chat.interrupt()
            else:
                print("[超时时LLM未在生成，无需中断]")

# 方法3：基于用户输入的中断控制
class InputControlledChat:
    """基于用户输入控制的聊天类"""
    
    def __init__(self, api_key: str, endpoint: str):
        self.chat = InterruptibleAzureOpenAI(api_key, endpoint)
        self.user_interrupt = asyncio.Event()
    
    async def wait_for_interrupt_command(self):
        """等待用户输入中断命令"""
        loop = asyncio.get_event_loop()
        # 在后台线程中等待用户输入
        def get_input():
            try:
                return input()
            except:
                return ""
        
        while not self.user_interrupt.is_set():
            try:
                # 使用 run_in_executor 在后台线程中处理输入
                user_input = await asyncio.wait_for(
                    loop.run_in_executor(None, get_input), 
                    timeout=0.1
                )
                if user_input.strip().lower() in ['stop', 'interrupt', 'quit']:
                    # 检查LLM状态后再中断
                    if self.chat.can_interrupt():
                        success = self.chat.interrupt()
                        if success:
                            print(f"[中断命令已执行]")
                        break
                    else:
                        print(f"[提示] LLM当前未在生成内容，无法中断")
            except asyncio.TimeoutError:
                continue
            except:
                break
    
    async def chat_with_input_control(self, message: str, model: str = "gpt-4"):
        """支持输入控制的对话"""
        self.user_interrupt.clear()
        
        print("AI正在回复... (输入 'stop' 或 'interrupt' 可中断)")
        print("AI: ", end="", flush=True)
        
        # 同时运行对话和中断监听
        chat_task = asyncio.create_task(self._stream_response(message, model))
        interrupt_task = asyncio.create_task(self.wait_for_interrupt_command())
        
        try:
            await asyncio.gather(chat_task, interrupt_task, return_exceptions=True)
        finally:
            # 清理任务
            chat_task.cancel()
            interrupt_task.cancel()
            self.user_interrupt.set()
    
    async def _stream_response(self, message: str, model: str):
        """内部流式响应方法"""
        async for chunk in self.chat.stream_chat_with_interrupt(message, model=model):
            print(chunk, end="", flush=True)
            await asyncio.sleep(0.01)

async def demo_different_interrupt_methods():
    """演示不同的中断方法"""
    API_KEY = os.getenv("AZURE_OPENAI_API_KEY", "your-api-key-here")
    ENDPOINT = os.getenv("AZURE_OPENAI_ENDPOINT", "your-endpoint-here")
    MODEL_NAME = os.getenv("AZURE_OPENAI_MODEL_NAME", "gpt-4")
    
    print("=== 中断方法演示 ===\n")
    
    # 方法1：超时中断
    print("1. 超时中断演示（5秒超时）:")
    timeout_chat = TimeoutInterruptibleChat(API_KEY, ENDPOINT)
    
    # 显示LLM状态
    status = timeout_chat.chat.get_llm_status()
    print(f"开始前LLM状态: 生成中={status['is_generating']}, 可中断={status['can_interrupt']}")
    
    await timeout_chat.chat_with_timeout(
        "请详细解释什么是深度学习，包括其历史发展、核心概念和应用领域", 
        timeout=5.0, 
        model=MODEL_NAME
    )
    
    print("\n\n" + "="*50 + "\n")
    
    # 方法2：输入控制中断
    print("2. 输入控制中断演示:")
    input_chat = InputControlledChat(API_KEY, ENDPOINT)
    
    # 显示LLM状态
    status = input_chat.chat.get_llm_status()
    print(f"开始前LLM状态: 生成中={status['is_generating']}, 可中断={status['can_interrupt']}")
    
    await input_chat.chat_with_input_control(
        "请写一个完整的Python Web应用程序示例", 
        model=MODEL_NAME
    )
    
    # 最终状态检查
    print(f"\n=== 最终状态检查 ===")
    for i, chat_obj in enumerate([timeout_chat.chat, input_chat.chat], 1):
        status = chat_obj.get_llm_status()
        print(f"Chat{i} - 生成中: {status['is_generating']}, 可中断: {status['can_interrupt']}")

if __name__ == "__main__":
    print("选择运行模式:")
    print("1. 交互式对话（支持按键中断）")
    print("2. 中断方法演示")
    print("3. 状态测试模式")
    
    choice = input("请选择 (1/2/3): ").strip()
    
    if choice == "1":
        asyncio.run(interactive_chat_with_interrupt())
    elif choice == "2":
        asyncio.run(demo_different_interrupt_methods())
    elif choice == "3":
        # 简单的状态测试
        async def test_status():
            API_KEY = "test-key"
            ENDPOINT = "test-endpoint"
            chat = InterruptibleAzureOpenAI(API_KEY, ENDPOINT)
            
            print("=== 状态测试 ===")
            status = chat.get_llm_status()
            print(f"初始状态: {status}")
            print(f"可以中断: {chat.can_interrupt()}")
            
            # 尝试中断（应该失败）
            result = chat.interrupt()
            print(f"中断结果: {result}")
        
        asyncio.run(test_status())
    else:
        print("无效选择，运行交互式对话")
        asyncio.run(interactive_chat_with_interrupt())


