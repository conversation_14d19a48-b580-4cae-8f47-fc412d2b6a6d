#!/usr/bin/env python3
"""
测试TTS功能修复的脚本
验证打断逻辑优化后TTS是否正常工作
"""

import asyncio
import sys
from pathlib import Path

# 添加路径
sys.path.append(str(Path(__file__).parent / "thirdparty" / "realtime_chat_v1"))
sys.path.append(str(Path(__file__).parent / "thirdparty" / "realtime_chat"))
sys.path.append(str(Path(__file__).parent / "thirdparty" / "tb_speech_search"))

async def test_queue_operations():
    """测试队列操作"""
    print("🔍 测试队列操作...")
    
    # 创建测试队列
    voice_queue = asyncio.Queue()
    voice_inqueue = asyncio.Queue()
    
    # 添加测试数据
    await voice_queue.put("test_voice_1")
    await voice_queue.put("test_voice_2")
    await voice_inqueue.put("test_tts_1")
    await voice_inqueue.put("test_tts_2")
    
    print(f"📊 初始状态 - voice_queue: {voice_queue.qsize()}, voice_inqueue: {voice_inqueue.qsize()}")
    
    # 定义清空函数（从app.py复制）
    async def clear_queue(queue):
        """异步清空队列的全局函数"""
        while not queue.empty():
            try:
                item = await queue.get()
                queue.task_done()
            except asyncio.QueueEmpty:
                break
    
    # 清空队列
    await clear_queue(voice_queue)
    await clear_queue(voice_inqueue)
    
    print(f"📊 清空后状态 - voice_queue: {voice_queue.qsize()}, voice_inqueue: {voice_inqueue.qsize()}")
    
    if voice_queue.qsize() == 0 and voice_inqueue.qsize() == 0:
        print("✅ 队列清空功能正常")
        return True
    else:
        print("❌ 队列清空功能异常")
        return False

def test_html_volumeControl_fix():
    """测试HTML中volumeControl对象修复"""
    print("\n🔍 测试HTML volumeControl修复...")
    
    try:
        html_path = Path("thirdparty/realtime_chat/index.html")
        if not html_path.exists():
            print("❌ HTML文件不存在")
            return False
        
        with open(html_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查volumeControl对象定义
        if "let volumeControl = {" in content:
            print("✅ volumeControl对象定义存在")
        else:
            print("❌ volumeControl对象定义缺失")
            return False
        
        # 检查必要的属性
        required_props = ["originalVolume", "currentVolume", "isReduced"]
        for prop in required_props:
            if prop in content:
                print(f"✅ {prop} 属性存在")
            else:
                print(f"❌ {prop} 属性缺失")
                return False
        
        # 检查函数定义
        required_functions = ["reduceVolume", "restoreVolume", "handleVolumeControl"]
        for func in required_functions:
            if f"function {func}" in content:
                print(f"✅ {func} 函数存在")
            else:
                print(f"❌ {func} 函数缺失")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 检查HTML文件失败: {e}")
        return False

def test_app_py_modifications():
    """测试app.py中的修改"""
    print("\n🔍 测试app.py修改...")
    
    try:
        with open("app.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查关键修改
        checks = [
            ("clear_queue_async方法", "async def clear_queue_async(self):"),
            ("voice_inqueue清空", "await clear_queue(self.voice_inqueue)"),
            ("同步清空voice_inqueue", "while not self.voice_inqueue.empty():"),
            ("优化的interrupt方法", "处理语音打断 - 优化版本，参考realtime_chat_v1"),
            ("TTS处理器恢复", "🔄 TTS处理器已恢复"),
        ]
        
        all_passed = True
        for check_name, check_pattern in checks:
            if check_pattern in content:
                print(f"✅ {check_name} 存在")
            else:
                print(f"❌ {check_name} 缺失")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查app.py文件失败: {e}")
        return False

def test_tts_processing_flow():
    """测试TTS处理流程"""
    print("\n🔍 测试TTS处理流程...")
    
    try:
        with open("app.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查TTS相关的关键流程
        tts_checks = [
            ("stream_tts_chunk方法", "async def stream_tts_chunk(self, chunk: str, conv_id: str):"),
            ("TTS chunk发送", "await self.voice_inqueue.put(chunk)"),
            ("TTS事件发送", "await self.voice_queue.put(AdditionalOutputs(tts_chunk_event))"),
            ("TTS处理器任务", "self.processer_task = asyncio.create_task(self.tts_server.run_stream_tts_processor("),
            ("TTS服务器恢复", "processer_task = await self.tts_server.resume(self.voice_inqueue, self.voice_queue)"),
        ]
        
        all_passed = True
        for check_name, check_pattern in tts_checks:
            if check_pattern in content:
                print(f"✅ {check_name} 存在")
            else:
                print(f"❌ {check_name} 缺失")
                all_passed = False
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查TTS处理流程失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始测试TTS功能修复...")
    print("=" * 50)
    
    tests = [
        ("队列操作测试", test_queue_operations),
        ("HTML volumeControl修复测试", test_html_volumeControl_fix),
        ("app.py修改测试", test_app_py_modifications),
        ("TTS处理流程测试", test_tts_processing_flow),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 执行: {test_name}")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 执行失败: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    success_count = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            success_count += 1
    
    total_tests = len(results)
    print(f"\n🎯 总体结果: {success_count}/{total_tests} 测试通过 ({success_count/total_tests*100:.1f}%)")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！TTS功能修复成功。")
        print("\n💡 修复内容总结:")
        print("1. ✅ 修复了HTML中缺失的volumeControl对象定义")
        print("2. ✅ 优化了打断逻辑，同时清空voice_queue和voice_inqueue")
        print("3. ✅ 改进了TTS处理器的恢复机制")
        print("4. ✅ 增强了错误处理和调试输出")
        print("\n🔧 建议测试:")
        print("- 启动应用并测试语音交互")
        print("- 测试语音打断功能")
        print("- 验证TTS播放是否正常")
        print("- 检查音量控制是否工作")
    else:
        print("⚠️  部分测试失败，需要进一步检查。")
        print("\n🔧 可能的问题:")
        print("- 检查文件路径是否正确")
        print("- 验证代码修改是否完整")
        print("- 确认依赖模块是否正常")
    
    return success_count == total_tests

if __name__ == "__main__":
    asyncio.run(main())
